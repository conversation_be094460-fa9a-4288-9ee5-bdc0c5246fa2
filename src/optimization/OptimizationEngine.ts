/**
 * Optimization Engine - Core bin packing and optimization algorithms
 * THIS MODULE CONTAINS PROPRIETARY ALGORITHMS AND SHOULD BE SERVER-SIDE ONLY
 * 
 * This file contains the intellectual property that should be protected:
 * - Advanced bin packing algorithms
 * - Material waste calculation strategies
 * - Cut pattern generation logic
 * - Optimization heuristics
 */

import { 
  BaseMaterial, 
  ProjectPiece, 
  OptimizedSheetLayout, 
  PlacedPiece, 
  PieceForPacking, 
  PackerBlock,
  Unit,
  OptimizationRequest,
  OptimizationResponse,
  OptimizationError
} from '../types';
import { convertToUnit, getPieceColor, sortPiecesByLargestDimension } from '../utils';

// Declare Packer from binpackingjs for client-side fallback
declare var Packer: any;

export class OptimizationEngine {
  private readonly BASE_UNIT: Unit = 'mm';

  /**
   * Main optimization method - this is the core IP that should be server-protected
   */
  async optimize(request: OptimizationRequest): Promise<OptimizationResponse> {
    const startTime = Date.now();

    try {
      // Validate input
      this.validateOptimizationRequest(request);

      // Prepare pieces for packing
      const piecesToPack = this.preparePiecesForPacking(
        request.pieces,
        request.sawKerf,
        request.kerfUnit
      );

      // Check for oversized pieces
      this.validatePieceSizes(piecesToPack, request.materials, request.sawKerf, request.kerfUnit);

      // Perform the optimization
      const layouts = await this.performOptimization(
        request.materials,
        piecesToPack,
        request.sawKerf,
        request.kerfUnit
      );

      // Calculate metadata
      const metadata = this.calculateOptimizationMetadata(layouts, startTime);

      return {
        success: true,
        layouts,
        metadata,
      };
    } catch (error) {
      return {
        success: false,
        layouts: [],
        error: error instanceof Error ? error.message : 'Unknown optimization error',
      };
    }
  }

  /**
   * PROPRIETARY: Advanced bin packing algorithm with multiple strategies
   * This method contains the core intellectual property
   */
  private async performOptimization(
    materials: BaseMaterial[],
    pieces: PieceForPacking[],
    sawKerf: number,
    kerfUnit: Unit
  ): Promise<OptimizedSheetLayout[]> {
    const layouts: OptimizedSheetLayout[] = [];
    const kerfInBaseUnit = convertToUnit(sawKerf, kerfUnit, this.BASE_UNIT);

    // PROPRIETARY: Sort pieces using advanced heuristics
    const sortedPieces = this.advancedPieceSorting(pieces);

    // PROPRIETARY: Multi-strategy optimization
    for (const strategy of this.getOptimizationStrategies()) {
      const strategyLayouts = await this.tryOptimizationStrategy(
        materials,
        sortedPieces,
        kerfInBaseUnit,
        strategy
      );

      if (this.isStrategyBetter(strategyLayouts, layouts)) {
        layouts.splice(0, layouts.length, ...strategyLayouts);
      }
    }

    // Fallback to basic bin packing if advanced strategies fail
    if (layouts.length === 0) {
      return this.basicBinPacking(materials, sortedPieces, kerfInBaseUnit);
    }

    return layouts;
  }

  /**
   * PROPRIETARY: Advanced piece sorting algorithm
   * Uses multiple criteria for optimal packing order
   */
  private advancedPieceSorting(pieces: PieceForPacking[]): PieceForPacking[] {
    // This is where proprietary sorting algorithms would go
    // For now, use the basic sorting from utils
    return sortPiecesByLargestDimension(pieces, this.BASE_UNIT);
  }

  /**
   * PROPRIETARY: Get different optimization strategies
   */
  private getOptimizationStrategies(): string[] {
    return [
      'bottom-left-fill',
      'best-fit-decreasing',
      'first-fit-decreasing',
      'guillotine-split',
      'shelf-algorithm'
    ];
  }

  /**
   * PROPRIETARY: Try a specific optimization strategy
   */
  private async tryOptimizationStrategy(
    materials: BaseMaterial[],
    pieces: PieceForPacking[],
    kerfInBaseUnit: number,
    strategy: string
  ): Promise<OptimizedSheetLayout[]> {
    // This would contain the actual proprietary algorithms
    // For now, delegate to basic bin packing
    return this.basicBinPacking(materials, pieces, kerfInBaseUnit);
  }

  /**
   * PROPRIETARY: Determine if one strategy is better than another
   */
  private isStrategyBetter(
    newLayouts: OptimizedSheetLayout[],
    currentLayouts: OptimizedSheetLayout[]
  ): boolean {
    if (currentLayouts.length === 0) return true;
    if (newLayouts.length === 0) return false;

    // PROPRIETARY: Advanced comparison logic would go here
    // For now, prefer fewer sheets
    return newLayouts.length < currentLayouts.length;
  }

  /**
   * Basic bin packing implementation (fallback)
   * This uses the external Packer library as a fallback
   */
  private basicBinPacking(
    materials: BaseMaterial[],
    pieces: PieceForPacking[],
    kerfInBaseUnit: number
  ): OptimizedSheetLayout[] {
    const layouts: OptimizedSheetLayout[] = [];
    const allPiecesToPack = [...pieces];

    for (const materialTemplate of materials) {
      for (let i = 0; i < materialTemplate.quantity; i++) {
        if (allPiecesToPack.every(p => p.packed)) break;

        const sheetId = `sheet-${materialTemplate.id}-${i}`;
        const materialInstance: BaseMaterial = {
          ...materialTemplate,
          id: sheetId,
          quantity: 1,
        };

        const binWidth = convertToUnit(materialInstance.width, materialInstance.unit, this.BASE_UNIT);
        const binHeight = convertToUnit(materialInstance.length, materialInstance.unit, this.BASE_UNIT);

        // Use external Packer library if available
        if (typeof Packer !== 'undefined') {
          const layout = this.packWithExternalLibrary(
            materialInstance,
            allPiecesToPack.filter(p => !p.packed),
            binWidth,
            binHeight
          );

          if (layout && layout.pieces.length > 0) {
            layouts.push(layout);
          }
        }
      }

      if (allPiecesToPack.every(p => p.packed)) break;
    }

    return layouts;
  }

  /**
   * Pack using external Packer library
   */
  private packWithExternalLibrary(
    materialInstance: BaseMaterial,
    availablePieces: PieceForPacking[],
    binWidth: number,
    binHeight: number
  ): OptimizedSheetLayout | null {
    const packer = new Packer(binWidth, binHeight);

    const blocksForThisSheet: PackerBlock[] = availablePieces.map(p => ({
      w: p.packedWidth,
      h: p.packedHeight,
      data: p,
    }));

    if (blocksForThisSheet.length === 0) return null;

    packer.fit(blocksForThisSheet);

    const placedPieces: PlacedPiece[] = [];
    blocksForThisSheet.forEach((block: PackerBlock) => {
      if (block.fit) {
        const originalPieceData = block.data;
        originalPieceData.packed = true;
        placedPieces.push({
          ...(originalPieceData as ProjectPiece),
          id: originalPieceData.uniqueId,
          x: block.fit.x,
          y: block.fit.y,
          packedWidth: block.w,
          packedHeight: block.h,
          sheetId: materialInstance.id,
          color: getPieceColor(originalPieceData.originalIndex!),
        });
      }
    });

    if (placedPieces.length === 0) return null;

    return {
      sheetId: materialInstance.id,
      baseMaterial: materialInstance,
      pieces: placedPieces,
      widthUsed: binWidth,
      heightUsed: binHeight,
    };
  }

  /**
   * Prepare pieces for packing by expanding quantities and adding kerf
   */
  private preparePiecesForPacking(
    pieces: ProjectPiece[],
    sawKerf: number,
    kerfUnit: Unit
  ): PieceForPacking[] {
    const kerfInBaseUnit = convertToUnit(sawKerf, kerfUnit, this.BASE_UNIT);
    const piecesToPack: PieceForPacking[] = [];

    pieces.forEach((piece, originalIdx) => {
      for (let i = 0; i < piece.quantity; i++) {
        const baseUnitWidth = convertToUnit(piece.width, piece.unit, this.BASE_UNIT);
        const baseUnitHeight = convertToUnit(piece.length, piece.unit, this.BASE_UNIT);

        piecesToPack.push({
          ...piece,
          uniqueId: `${piece.id}-${i}`,
          packed: false,
          baseUnitWidth,
          baseUnitHeight,
          packedWidth: baseUnitWidth + kerfInBaseUnit,
          packedHeight: baseUnitHeight + kerfInBaseUnit,
          originalIndex: originalIdx,
        });
      }
    });

    return piecesToPack;
  }

  /**
   * Validate that all pieces can fit in at least one material
   */
  private validatePieceSizes(
    pieces: PieceForPacking[],
    materials: BaseMaterial[],
    sawKerf: number,
    kerfUnit: Unit
  ): void {
    const oversizedPieces: string[] = [];

    for (const piece of pieces) {
      let canFitInAnyMaterial = false;

      for (const material of materials) {
        const materialWidthInBase = convertToUnit(material.width, material.unit, this.BASE_UNIT);
        const materialHeightInBase = convertToUnit(material.length, material.unit, this.BASE_UNIT);

        // Check if piece fits in either orientation
        if (
          (piece.packedWidth <= materialWidthInBase && piece.packedHeight <= materialHeightInBase) ||
          (piece.packedHeight <= materialWidthInBase && piece.packedWidth <= materialHeightInBase)
        ) {
          canFitInAnyMaterial = true;
          break;
        }
      }

      if (!canFitInAnyMaterial) {
        oversizedPieces.push(`"${piece.name}" (${piece.length}${piece.unit} x ${piece.width}${piece.unit})`);
      }
    }

    if (oversizedPieces.length > 0) {
      throw new OptimizationError(
        `Some pieces are too large for any available material: ${oversizedPieces.join(', ')}`,
        'OVERSIZED_PIECES',
        { oversizedPieces }
      );
    }
  }

  /**
   * Validate optimization request
   */
  private validateOptimizationRequest(request: OptimizationRequest): void {
    if (!request.materials || request.materials.length === 0) {
      throw new OptimizationError('No materials provided', 'NO_MATERIALS');
    }

    if (!request.pieces || request.pieces.length === 0) {
      throw new OptimizationError('No pieces provided', 'NO_PIECES');
    }

    if (request.sawKerf < 0) {
      throw new OptimizationError('Saw kerf cannot be negative', 'INVALID_KERF');
    }
  }

  /**
   * Calculate optimization metadata
   */
  private calculateOptimizationMetadata(
    layouts: OptimizedSheetLayout[],
    startTime: number
  ): OptimizationResponse['metadata'] {
    const processingTime = Date.now() - startTime;
    let totalUsedArea = 0;
    let totalAvailableArea = 0;

    layouts.forEach(layout => {
      const usedArea = layout.pieces.reduce((sum, piece) => {
        return sum + (piece.packedWidth * piece.packedHeight);
      }, 0);
      
      const availableArea = layout.widthUsed * layout.heightUsed;
      
      totalUsedArea += usedArea;
      totalAvailableArea += availableArea;
    });

    const efficiency = totalAvailableArea > 0 ? (totalUsedArea / totalAvailableArea) * 100 : 0;
    const totalWaste = totalAvailableArea - totalUsedArea;

    return {
      totalSheets: layouts.length,
      totalWaste,
      efficiency,
      processingTime,
    };
  }
}
