/**
 * Tab Container Component
 * Provides tabbed interface for organizing content
 * Used in project detail view for Cutting List and Visualization tabs
 */

import { TabContainerProps, TabDefinition } from '../../types';

export class TabContainer {
  private container: HTMLElement;
  private props: TabContainerProps;
  private tabsContainer: HTMLElement | null = null;
  private contentContainer: HTMLElement | null = null;

  constructor(container: HTMLElement, props: TabContainerProps) {
    this.container = container;
    this.props = props;
    this.createTabContainer();
    this.bindEvents();
    this.render();
  }

  /**
   * Update component props and re-render
   */
  updateProps(newProps: TabContainerProps): void {
    this.props = newProps;
    this.render();
  }

  /**
   * Create the tab container HTML structure
   */
  private createTabContainer(): void {
    this.container.innerHTML = `
      <div class="tab-container">
        <div class="tab-header">
          <div class="tab-list" role="tablist">
            <!-- Tab buttons will be rendered here -->
          </div>
        </div>
        <div class="tab-content">
          <!-- Tab content will be rendered here -->
        </div>
      </div>
    `;

    this.tabsContainer = this.container.querySelector('.tab-list');
    this.contentContainer = this.container.querySelector('.tab-content');
  }

  /**
   * Bind event listeners
   */
  private bindEvents(): void {
    // Tab click events will be bound during render
  }

  /**
   * Render the tab container
   */
  private render(): void {
    this.renderTabs();
    this.renderContent();
  }

  /**
   * Render tab buttons
   */
  private renderTabs(): void {
    if (!this.tabsContainer) return;

    const tabsHTML = this.props.tabs.map(tab => `
      <button 
        class="tab-button ${tab.id === this.props.activeTabId ? 'active' : ''}"
        data-tab-id="${tab.id}"
        role="tab"
        aria-selected="${tab.id === this.props.activeTabId}"
        aria-controls="tab-panel-${tab.id}"
        id="tab-${tab.id}"
      >
        ${tab.icon ? `<span class="tab-icon">${tab.icon}</span>` : ''}
        <span class="tab-label">${tab.label}</span>
      </button>
    `).join('');

    this.tabsContainer.innerHTML = tabsHTML;

    // Bind tab click events
    const tabButtons = this.tabsContainer.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const tabId = (e.currentTarget as HTMLElement).dataset.tabId;
        if (tabId) {
          this.props.onTabChange(tabId);
        }
      });
    });

    // Add keyboard navigation
    this.addKeyboardNavigation();
  }

  /**
   * Render tab content
   */
  private renderContent(): void {
    if (!this.contentContainer) return;

    // Clear existing content
    this.contentContainer.innerHTML = '';

    // Find active tab
    const activeTab = this.props.tabs.find(tab => tab.id === this.props.activeTabId);
    if (!activeTab) return;

    // Create content panel
    const contentPanel = document.createElement('div');
    contentPanel.className = 'tab-panel';
    contentPanel.id = `tab-panel-${activeTab.id}`;
    contentPanel.setAttribute('role', 'tabpanel');
    contentPanel.setAttribute('aria-labelledby', `tab-${activeTab.id}`);

    // Add content
    if (typeof activeTab.content === 'string') {
      contentPanel.innerHTML = activeTab.content;
    } else {
      // Clear the content element and append it
      activeTab.content.innerHTML = activeTab.content.innerHTML;
      contentPanel.appendChild(activeTab.content);
    }

    this.contentContainer.appendChild(contentPanel);
  }

  /**
   * Add keyboard navigation support
   */
  private addKeyboardNavigation(): void {
    if (!this.tabsContainer) return;

    const tabButtons = this.tabsContainer.querySelectorAll('.tab-button');
    
    tabButtons.forEach((button, index) => {
      button.addEventListener('keydown', (e) => {
        const key = (e as KeyboardEvent).key;
        let targetIndex = index;

        switch (key) {
          case 'ArrowLeft':
            (e as KeyboardEvent).preventDefault();
            targetIndex = index > 0 ? index - 1 : tabButtons.length - 1;
            break;
          case 'ArrowRight':
            (e as KeyboardEvent).preventDefault();
            targetIndex = index < tabButtons.length - 1 ? index + 1 : 0;
            break;
          case 'Home':
            (e as KeyboardEvent).preventDefault();
            targetIndex = 0;
            break;
          case 'End':
            (e as KeyboardEvent).preventDefault();
            targetIndex = tabButtons.length - 1;
            break;
          default:
            return;
        }

        const targetButton = tabButtons[targetIndex] as HTMLElement;
        targetButton.focus();
        
        // Optionally activate the tab on focus (remove if you want click-only activation)
        const tabId = targetButton.dataset.tabId;
        if (tabId) {
          this.props.onTabChange(tabId);
        }
      });
    });
  }

  /**
   * Add a new tab
   */
  addTab(tab: TabDefinition): void {
    this.props.tabs.push(tab);
    this.render();
  }

  /**
   * Remove a tab
   */
  removeTab(tabId: string): void {
    this.props.tabs = this.props.tabs.filter(tab => tab.id !== tabId);
    
    // If we removed the active tab, switch to the first available tab
    if (this.props.activeTabId === tabId && this.props.tabs.length > 0) {
      this.props.onTabChange(this.props.tabs[0].id);
    }
    
    this.render();
  }

  /**
   * Update tab content
   */
  updateTabContent(tabId: string, content: HTMLElement | string): void {
    const tab = this.props.tabs.find(t => t.id === tabId);
    if (tab) {
      tab.content = content;
      if (tabId === this.props.activeTabId) {
        this.renderContent();
      }
    }
  }

  /**
   * Get the active tab ID
   */
  getActiveTabId(): string {
    return this.props.activeTabId;
  }

  /**
   * Get tab container element for styling
   */
  getTabContainerElement(): HTMLElement {
    return this.container.querySelector('.tab-container') as HTMLElement;
  }

  /**
   * Destroy the component
   */
  destroy(): void {
    // Remove event listeners
    if (this.tabsContainer) {
      const tabButtons = this.tabsContainer.querySelectorAll('.tab-button');
      tabButtons.forEach(button => {
        button.removeEventListener('click', () => {});
        button.removeEventListener('keydown', () => {});
      });
    }

    // Clear container
    this.container.innerHTML = '';
  }
}
