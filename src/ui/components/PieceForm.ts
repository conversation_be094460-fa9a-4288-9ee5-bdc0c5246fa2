/**
 * Piece Form Component - Handles piece input and editing
 */

import { ProjectPiece, PieceFormProps, Unit, GrainDirection } from '../../types';
import { isPositiveNumber } from '../../utils';

export class PieceForm {
  private container: HTMLElement;
  private props: PieceFormProps;
  private isEditing: boolean = false;

  // Form elements
  private nameInput!: HTMLInputElement;
  private lengthInput!: HTMLInputElement;
  private widthInput!: HTMLInputElement;
  private unitSelect!: HTMLSelectElement;
  private quantityInput!: HTMLInputElement;
  private grainDirectionSelect!: HTMLSelectElement;
  private submitButton!: HTMLButtonElement;
  private cancelButton!: HTMLButtonElement;

  constructor(container: HTMLElement, props: PieceFormProps) {
    this.container = container;
    this.props = props;
    this.createForm();
    this.bindEvents();
  }

  /**
   * Update props and re-render if needed
   */
  updateProps(newProps: PieceFormProps): void {
    const wasEditing = this.isEditing;
    this.props = newProps;
    this.isEditing = !!newProps.editingPiece;

    if (this.isEditing && newProps.editingPiece) {
      this.populateForm(newProps.editingPiece);
      this.submitButton.textContent = 'Update Piece';
      this.cancelButton.style.display = 'inline-block';
    } else if (wasEditing && !this.isEditing) {
      this.resetForm();
    }
  }

  /**
   * Create the form HTML structure
   */
  private createForm(): void {
    this.container.innerHTML = `
      <div class="form-group">
        <label for="pieceName">Piece Name (e.g., Table Top)</label>
        <input type="text" id="pieceName" placeholder="Table Top" />
      </div>
      <div class="inline-inputs">
        <div class="form-group">
          <label for="pieceLength">Length</label>
          <input type="number" id="pieceLength" placeholder="e.g., 1200" />
        </div>
        <div class="form-group">
          <label for="pieceWidth">Width</label>
          <input type="number" id="pieceWidth" placeholder="e.g., 600" />
        </div>
        <div class="form-group">
          <label for="pieceUnit">Unit</label>
          <select id="pieceUnit">
            <option value="mm">mm</option>
            <option value="cm">cm</option>
            <option value="in">inches</option>
          </select>
        </div>
      </div>
      <div class="inline-inputs">
        <div class="form-group">
          <label for="pieceQuantity">Quantity</label>
          <input type="number" id="pieceQuantity" value="1" min="1" />
        </div>
        <div class="form-group">
          <label for="grainDirection">Grain Direction</label>
          <select id="grainDirection">
            <option value="none">No Preference</option>
            <option value="length">Along Length</option>
            <option value="width">Along Width</option>
          </select>
        </div>
      </div>
      <div class="form-buttons">
        <button id="submitPieceBtn" type="button" aria-label="Add piece to project">
          Add Piece
        </button>
        <button id="cancelPieceBtn" type="button" class="secondary" style="display: none;" aria-label="Cancel editing">
          Cancel
        </button>
      </div>
    `;

    // Get references to form elements
    this.nameInput = this.container.querySelector('#pieceName') as HTMLInputElement;
    this.lengthInput = this.container.querySelector('#pieceLength') as HTMLInputElement;
    this.widthInput = this.container.querySelector('#pieceWidth') as HTMLInputElement;
    this.unitSelect = this.container.querySelector('#pieceUnit') as HTMLSelectElement;
    this.quantityInput = this.container.querySelector('#pieceQuantity') as HTMLInputElement;
    this.grainDirectionSelect = this.container.querySelector('#grainDirection') as HTMLSelectElement;
    this.submitButton = this.container.querySelector('#submitPieceBtn') as HTMLButtonElement;
    this.cancelButton = this.container.querySelector('#cancelPieceBtn') as HTMLButtonElement;
  }

  /**
   * Bind event listeners
   */
  private bindEvents(): void {
    this.submitButton.addEventListener('click', () => this.handleSubmit());
    this.cancelButton.addEventListener('click', () => this.handleCancel());

    // Add input validation
    this.lengthInput.addEventListener('input', () => this.validateInput(this.lengthInput));
    this.widthInput.addEventListener('input', () => this.validateInput(this.widthInput));
    this.quantityInput.addEventListener('input', () => this.validateInput(this.quantityInput));

    // Handle Enter key
    this.container.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        this.handleSubmit();
      }
    });
  }

  /**
   * Handle form submission
   */
  private handleSubmit(): void {
    try {
      const pieceData = this.getPieceData();
      this.validatePieceData(pieceData);

      if (this.isEditing && this.props.editingPiece) {
        this.props.onUpdate(this.props.editingPiece.id, pieceData);
      } else {
        this.props.onAdd(pieceData);
      }

      this.resetForm();
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Invalid piece data');
    }
  }

  /**
   * Handle cancel editing
   */
  private handleCancel(): void {
    this.props.onCancelEdit();
    this.resetForm();
  }

  /**
   * Get piece data from form
   */
  private getPieceData(): Omit<ProjectPiece, 'id'> {
    const name = this.nameInput.value.trim() || `Piece ${Date.now()}`;
    const length = parseFloat(this.lengthInput.value);
    const width = parseFloat(this.widthInput.value);
    const unit = this.unitSelect.value as Unit;
    const quantity = parseInt(this.quantityInput.value, 10);
    const grainDirection = this.grainDirectionSelect.value as GrainDirection;

    return {
      name,
      length,
      width,
      unit,
      quantity,
      grainDirection,
    };
  }

  /**
   * Validate piece data
   */
  private validatePieceData(data: Omit<ProjectPiece, 'id'>): void {
    if (!data.name) {
      throw new Error('Piece name is required');
    }

    if (!isPositiveNumber(data.length)) {
      throw new Error('Length must be a positive number');
    }

    if (!isPositiveNumber(data.width)) {
      throw new Error('Width must be a positive number');
    }

    if (!isPositiveNumber(data.quantity)) {
      throw new Error('Quantity must be a positive number');
    }

    if (!['mm', 'cm', 'in'].includes(data.unit)) {
      throw new Error('Invalid unit selected');
    }

    if (!['none', 'length', 'width'].includes(data.grainDirection)) {
      throw new Error('Invalid grain direction selected');
    }
  }

  /**
   * Validate individual input field
   */
  private validateInput(input: HTMLInputElement): void {
    const value = parseFloat(input.value);
    
    if (input.type === 'number') {
      if (isNaN(value) || value <= 0) {
        input.setCustomValidity('Must be a positive number');
        input.classList.add('invalid');
      } else {
        input.setCustomValidity('');
        input.classList.remove('invalid');
      }
    }
  }

  /**
   * Populate form with piece data for editing
   */
  private populateForm(piece: ProjectPiece): void {
    this.nameInput.value = piece.name;
    this.lengthInput.value = piece.length.toString();
    this.widthInput.value = piece.width.toString();
    this.unitSelect.value = piece.unit;
    this.quantityInput.value = piece.quantity.toString();
    this.grainDirectionSelect.value = piece.grainDirection;
  }

  /**
   * Reset form to initial state
   */
  private resetForm(): void {
    this.nameInput.value = '';
    this.lengthInput.value = '';
    this.widthInput.value = '';
    this.quantityInput.value = '1';
    this.grainDirectionSelect.value = 'none';
    this.submitButton.textContent = 'Add Piece';
    this.cancelButton.style.display = 'none';
    this.isEditing = false;

    // Clear validation states
    [this.lengthInput, this.widthInput, this.quantityInput].forEach(input => {
      input.setCustomValidity('');
      input.classList.remove('invalid');
    });
  }

  /**
   * Focus on the first input
   */
  focus(): void {
    this.nameInput.focus();
  }

  /**
   * Get current form state
   */
  getFormState(): {
    isEditing: boolean;
    hasData: boolean;
    isValid: boolean;
  } {
    const hasData = !!(
      this.nameInput.value.trim() ||
      this.lengthInput.value ||
      this.widthInput.value ||
      this.quantityInput.value !== '1' ||
      this.grainDirectionSelect.value !== 'none'
    );

    let isValid = true;
    try {
      if (hasData) {
        this.validatePieceData(this.getPieceData());
      }
    } catch {
      isValid = false;
    }

    return {
      isEditing: this.isEditing,
      hasData,
      isValid,
    };
  }

  /**
   * Set form enabled/disabled state
   */
  setEnabled(enabled: boolean): void {
    const inputs = [
      this.nameInput,
      this.lengthInput,
      this.widthInput,
      this.unitSelect,
      this.quantityInput,
      this.grainDirectionSelect,
    ];

    inputs.forEach(input => {
      input.disabled = !enabled;
    });

    this.submitButton.disabled = !enabled;
    this.cancelButton.disabled = !enabled;
  }

  /**
   * Destroy the component
   */
  destroy(): void {
    // Remove event listeners and clean up
    this.container.innerHTML = '';
  }
}
