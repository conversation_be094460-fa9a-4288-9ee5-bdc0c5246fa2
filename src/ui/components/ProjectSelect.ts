/**
 * Project Select Component - Handles project selection and management
 */

import { Project, ProjectSelectProps } from '../../types';

export class ProjectSelect {
  private container: HTMLElement;
  private props: ProjectSelectProps;

  // Form elements
  private projectSelect!: HTMLSelectElement;
  private newProjectInput!: HTMLInputElement;
  private createButton!: HTMLButtonElement;
  private deleteButton!: HTMLButtonElement;

  constructor(container: HTMLElement, props: ProjectSelectProps) {
    this.container = container;
    this.props = props;
    this.createComponent();
    this.bindEvents();
    this.render();
  }

  /**
   * Update props and re-render
   */
  updateProps(newProps: ProjectSelectProps): void {
    this.props = newProps;
    this.render();
  }

  /**
   * Create the component HTML structure
   */
  private createComponent(): void {
    this.container.innerHTML = `
      <div class="form-group">
        <label for="projectSelect">Current Project</label>
        <select id="projectSelect" aria-label="Select current project">
          <!-- Options populated by render method -->
        </select>
      </div>
      <div class="inline-inputs">
        <div class="form-group">
          <label for="newProjectName">New Project Name</label>
          <input type="text" id="newProjectName" placeholder="e.g., Kitchen Cabinets" />
        </div>
        <div class="form-group project-buttons">
          <button id="createProjectBtn" type="button" aria-label="Create new project">
            Create Project
          </button>
          <button id="deleteProjectBtn" type="button" class="danger-btn" aria-label="Delete current project">
            Delete Project
          </button>
        </div>
      </div>
    `;

    // Get references to elements
    this.projectSelect = this.container.querySelector('#projectSelect') as HTMLSelectElement;
    this.newProjectInput = this.container.querySelector('#newProjectName') as HTMLInputElement;
    this.createButton = this.container.querySelector('#createProjectBtn') as HTMLButtonElement;
    this.deleteButton = this.container.querySelector('#deleteProjectBtn') as HTMLButtonElement;
  }

  /**
   * Bind event listeners
   */
  private bindEvents(): void {
    this.projectSelect.addEventListener('change', () => this.handleProjectChange());
    this.createButton.addEventListener('click', () => this.handleCreateProject());
    this.deleteButton.addEventListener('click', () => this.handleDeleteProject());

    // Handle Enter key in new project input
    this.newProjectInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        e.preventDefault();
        this.handleCreateProject();
      }
    });

    // Validate project name as user types
    this.newProjectInput.addEventListener('input', () => this.validateProjectName());
  }

  /**
   * Render the component
   */
  private render(): void {
    this.renderProjectOptions();
    this.updateButtonStates();
  }

  /**
   * Render project options in select
   */
  private renderProjectOptions(): void {
    this.projectSelect.innerHTML = '';
    
    this.props.projects.forEach(project => {
      const option = document.createElement('option');
      option.value = project.id;
      option.textContent = project.name;
      option.selected = project.id === this.props.currentProjectId;
      this.projectSelect.appendChild(option);
    });

    // Add placeholder if no projects
    if (this.props.projects.length === 0) {
      const option = document.createElement('option');
      option.value = '';
      option.textContent = 'No projects available';
      option.disabled = true;
      this.projectSelect.appendChild(option);
    }
  }

  /**
   * Update button states based on current state
   */
  private updateButtonStates(): void {
    // Disable delete if only one project or no current project
    this.deleteButton.disabled = 
      this.props.projects.length <= 1 || 
      !this.props.currentProjectId;

    // Update create button state based on input validation
    this.validateProjectName();
  }

  /**
   * Handle project selection change
   */
  private handleProjectChange(): void {
    const selectedProjectId = this.projectSelect.value;
    if (selectedProjectId && selectedProjectId !== this.props.currentProjectId) {
      this.props.onProjectChange(selectedProjectId);
    }
  }

  /**
   * Handle create new project
   */
  private handleCreateProject(): void {
    const name = this.newProjectInput.value.trim();
    
    if (!name) {
      alert('Please enter a project name.');
      this.newProjectInput.focus();
      return;
    }

    // Check for duplicate names
    if (this.props.projects.some(p => p.name.toLowerCase() === name.toLowerCase())) {
      alert('A project with this name already exists.');
      this.newProjectInput.focus();
      this.newProjectInput.select();
      return;
    }

    try {
      this.props.onCreateProject(name);
      this.newProjectInput.value = '';
      this.validateProjectName();
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to create project');
    }
  }

  /**
   * Handle delete current project
   */
  private handleDeleteProject(): void {
    if (!this.props.currentProjectId) {
      alert('No project selected to delete.');
      return;
    }

    if (this.props.projects.length <= 1) {
      alert('Cannot delete the last project.');
      return;
    }

    const currentProject = this.props.projects.find(p => p.id === this.props.currentProjectId);
    if (!currentProject) {
      alert('Current project not found.');
      return;
    }

    const confirmMessage = `Are you sure you want to delete the project "${currentProject.name}"? This action cannot be undone.`;
    if (!confirm(confirmMessage)) {
      return;
    }

    try {
      this.props.onDeleteProject(this.props.currentProjectId);
    } catch (error) {
      alert(error instanceof Error ? error.message : 'Failed to delete project');
    }
  }

  /**
   * Validate project name input
   */
  private validateProjectName(): void {
    const name = this.newProjectInput.value.trim();
    const isDuplicate = this.props.projects.some(p => 
      p.name.toLowerCase() === name.toLowerCase()
    );

    if (!name) {
      this.createButton.disabled = true;
      this.newProjectInput.setCustomValidity('');
      this.newProjectInput.classList.remove('invalid');
    } else if (isDuplicate) {
      this.createButton.disabled = true;
      this.newProjectInput.setCustomValidity('Project name already exists');
      this.newProjectInput.classList.add('invalid');
    } else {
      this.createButton.disabled = false;
      this.newProjectInput.setCustomValidity('');
      this.newProjectInput.classList.remove('invalid');
    }
  }

  /**
   * Get current project info
   */
  getCurrentProject(): Project | null {
    if (!this.props.currentProjectId) return null;
    return this.props.projects.find(p => p.id === this.props.currentProjectId) || null;
  }

  /**
   * Focus on new project input
   */
  focusNewProject(): void {
    this.newProjectInput.focus();
  }

  /**
   * Clear new project input
   */
  clearNewProject(): void {
    this.newProjectInput.value = '';
    this.validateProjectName();
  }

  /**
   * Set component enabled/disabled state
   */
  setEnabled(enabled: boolean): void {
    this.projectSelect.disabled = !enabled;
    this.newProjectInput.disabled = !enabled;
    this.createButton.disabled = !enabled || !this.newProjectInput.value.trim();
    this.deleteButton.disabled = !enabled || this.props.projects.length <= 1;
  }

  /**
   * Get component state
   */
  getState(): {
    currentProjectId: string | null;
    projectCount: number;
    hasValidNewName: boolean;
    canDelete: boolean;
  } {
    const name = this.newProjectInput.value.trim();
    const isDuplicate = this.props.projects.some(p => 
      p.name.toLowerCase() === name.toLowerCase()
    );

    return {
      currentProjectId: this.props.currentProjectId,
      projectCount: this.props.projects.length,
      hasValidNewName: !!name && !isDuplicate,
      canDelete: this.props.projects.length > 1 && !!this.props.currentProjectId,
    };
  }

  /**
   * Export projects data
   */
  exportProjects(): string {
    return JSON.stringify(this.props.projects, null, 2);
  }

  /**
   * Get project statistics
   */
  getProjectStats(): {
    totalProjects: number;
    totalPieces: number;
    averagePiecesPerProject: number;
    oldestProject: Date | null;
    newestProject: Date | null;
  } {
    const projects = this.props.projects;
    const totalPieces = projects.reduce((sum, p) => sum + p.pieces.length, 0);
    
    let oldestDate: Date | null = null;
    let newestDate: Date | null = null;

    projects.forEach(project => {
      if (!oldestDate || project.createdAt < oldestDate) {
        oldestDate = project.createdAt;
      }
      if (!newestDate || project.createdAt > newestDate) {
        newestDate = project.createdAt;
      }
    });

    return {
      totalProjects: projects.length,
      totalPieces,
      averagePiecesPerProject: projects.length > 0 ? totalPieces / projects.length : 0,
      oldestProject: oldestDate,
      newestProject: newestDate,
    };
  }

  /**
   * Destroy the component
   */
  destroy(): void {
    this.container.innerHTML = '';
  }
}
