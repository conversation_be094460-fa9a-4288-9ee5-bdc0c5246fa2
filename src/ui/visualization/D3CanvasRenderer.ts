/**
 * D3 Canvas Renderer - <PERSON>les visualization of cutting layouts using d3.js
 */

import * as d3 from 'd3';
import { OptimizedSheetLayout, VisualizationProps, Unit, PlacedPiece } from '../../types';
import { convertToUnit, formatNumber } from '../../utils';

export class D3CanvasRenderer {
  private container: HTMLElement;
  private svg!: d3.Selection<SVGSVGElement, unknown, null, undefined>;
  private props: VisualizationProps;
  private baseUnit: Unit = 'mm';

  // Navigation elements
  private prevButton: HTMLButtonElement;
  private nextButton: HTMLButtonElement;
  private sheetIndicator: HTMLSpanElement;

  // Interactive state
  private selectedPiece: PlacedPiece | null = null;
  private hoveredPiece: PlacedPiece | null = null;
  private pieceInfoPanel: HTMLDivElement | null = null;

  // D3 scales and dimensions
  private width: number = 800;
  private height: number = 600;
  private margin = { top: 40, right: 40, bottom: 40, left: 40 };

  constructor(
    container: HTMLElement,
    props: VisualizationProps,
    navigationElements: {
      prevButton: HTMLButtonElement;
      nextButton: HTMLButtonElement;
      sheetIndicator: HTMLSpanElement;
    }
  ) {
    this.container = container;
    this.props = props;
    this.prevButton = navigationElements.prevButton;
    this.nextButton = navigationElements.nextButton;
    this.sheetIndicator = navigationElements.sheetIndicator;

    this.initializeSVG();
    this.createPieceInfoPanel();
    this.bindEvents();
    this.render();
  }

  /**
   * Initialize SVG container
   */
  private initializeSVG(): void {
    // Remove any existing SVG
    d3.select(this.container).select('svg').remove();

    // Create new SVG
    this.svg = d3.select(this.container)
      .append('svg')
      .attr('width', '100%')
      .attr('height', '100%')
      .attr('viewBox', `0 0 ${this.width} ${this.height}`)
      .attr('preserveAspectRatio', 'xMidYMid meet')
      .style('background-color', '#fff')
      .style('border', '1px solid #bdc3c7');

    // Add defs for gradients and patterns
    const defs = this.svg.append('defs');
    
    // Create shadow filter
    const filter = defs.append('filter')
      .attr('id', 'drop-shadow')
      .attr('x', '-20%')
      .attr('y', '-20%')
      .attr('width', '140%')
      .attr('height', '140%');

    filter.append('feDropShadow')
      .attr('dx', 2)
      .attr('dy', 2)
      .attr('stdDeviation', 3)
      .attr('flood-opacity', 0.3);
  }

  /**
   * Update props and re-render
   */
  updateProps(newProps: VisualizationProps): void {
    this.props = newProps;
    this.selectedPiece = null;
    this.hoveredPiece = null;
    this.updatePieceInfoPanel(null);
    this.render();
  }

  /**
   * Create piece information panel
   */
  private createPieceInfoPanel(): void {
    this.pieceInfoPanel = document.createElement('div');
    this.pieceInfoPanel.className = 'piece-info-panel';
    this.pieceInfoPanel.style.cssText = `
      position: absolute;
      background: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 12px;
      border-radius: 6px;
      font-size: 12px;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      pointer-events: none;
      z-index: 1000;
      display: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      max-width: 200px;
      line-height: 1.4;
      top: 10px;
      right: 10px;
      transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
      transform: translateY(0);
      opacity: 0;
    `;

    this.container.appendChild(this.pieceInfoPanel);
  }

  /**
   * Bind navigation and interaction events
   */
  private bindEvents(): void {
    this.prevButton.addEventListener('click', () => {
      if (this.props.currentSheetIndex > 0) {
        this.props.onSheetChange(this.props.currentSheetIndex - 1);
      }
    });

    this.nextButton.addEventListener('click', () => {
      if (this.props.currentSheetIndex < this.props.layouts.length - 1) {
        this.props.onSheetChange(this.props.currentSheetIndex + 1);
      }
    });
  }

  /**
   * Main render method
   */
  private render(): void {
    this.updateNavigation();

    if (this.props.layouts.length === 0) {
      this.renderEmptyState();
      return;
    }

    const layout = this.props.layouts[this.props.currentSheetIndex];
    if (!layout) {
      this.renderEmptyState();
      return;
    }

    this.renderLayout(layout);
  }

  /**
   * Render empty state when no layouts available
   */
  private renderEmptyState(): void {
    this.svg.selectAll('*').remove();
    
    this.svg.append('text')
      .attr('x', this.width / 2)
      .attr('y', this.height / 2)
      .attr('text-anchor', 'middle')
      .attr('dominant-baseline', 'middle')
      .style('font-size', '14px')
      .style('fill', '#777')
      .text('No layout to display. Add items and optimize.');
  }

  /**
   * Render a specific layout
   */
  private renderLayout(layout: OptimizedSheetLayout): void {
    // Clear previous content
    this.svg.selectAll('*:not(defs)').remove();

    // Calculate dimensions and scaling
    const containerWidth = this.width - this.margin.left - this.margin.right;
    const containerHeight = this.height - this.margin.top - this.margin.bottom;
    
    const scaleX = containerWidth / layout.widthUsed;
    const scaleY = containerHeight / layout.heightUsed;
    const scale = Math.min(scaleX, scaleY) * 0.9; // 90% to leave margin

    const drawingWidth = layout.widthUsed * scale;
    const drawingHeight = layout.heightUsed * scale;
    const offsetX = (this.width - drawingWidth) / 2;
    const offsetY = (this.height - drawingHeight) / 2;

    // Create main group for the layout
    const layoutGroup = this.svg.append('g')
      .attr('class', 'layout-group')
      .attr('transform', `translate(${offsetX}, ${offsetY})`);

    // Draw material sheet outline
    this.drawMaterialOutline(layoutGroup, drawingWidth, drawingHeight);

    // Draw dimension labels
    this.drawDimensionLabels(layoutGroup, layout, drawingWidth, drawingHeight);

    // Draw pieces
    this.drawPieces(layoutGroup, layout.pieces, scale);
  }

  /**
   * Draw material sheet outline
   */
  private drawMaterialOutline(
    group: d3.Selection<SVGGElement, unknown, null, undefined>,
    width: number,
    height: number
  ): void {
    group.append('rect')
      .attr('x', 0)
      .attr('y', 0)
      .attr('width', width)
      .attr('height', height)
      .style('fill', '#fafafa')
      .style('stroke', '#333')
      .style('stroke-width', 2);
  }

  /**
   * Draw dimension labels
   */
  private drawDimensionLabels(
    group: d3.Selection<SVGGElement, unknown, null, undefined>,
    layout: OptimizedSheetLayout,
    drawingWidth: number,
    drawingHeight: number
  ): void {
    // Top label: Material Length
    const lengthLabel = `Length: ${layout.baseMaterial.length}${layout.baseMaterial.unit} (${formatNumber(layout.heightUsed)}${this.baseUnit})`;
    group.append('text')
      .attr('x', drawingWidth / 2)
      .attr('y', -10)
      .attr('text-anchor', 'middle')
      .style('font-size', '10px')
      .style('fill', '#555')
      .text(lengthLabel);

    // Side label: Material Width
    const widthLabel = `Width: ${layout.baseMaterial.width}${layout.baseMaterial.unit} (${formatNumber(layout.widthUsed)}${this.baseUnit})`;
    group.append('text')
      .attr('x', -10)
      .attr('y', drawingHeight / 2)
      .attr('text-anchor', 'middle')
      .attr('transform', `rotate(-90, -10, ${drawingHeight / 2})`)
      .style('font-size', '10px')
      .style('fill', '#555')
      .text(widthLabel);
  }

  /**
   * Draw pieces using d3 data binding
   */
  private drawPieces(
    group: d3.Selection<SVGGElement, unknown, null, undefined>,
    pieces: PlacedPiece[],
    scale: number
  ): void {
    const pieceGroups = group.selectAll('.piece-group')
      .data(pieces, (d: any) => d.id)
      .enter()
      .append('g')
      .attr('class', 'piece-group')
      .attr('transform', (d: PlacedPiece) => `translate(${d.x * scale}, ${d.y * scale})`);

    // Draw each piece
    pieceGroups.each((d: PlacedPiece, i: number, nodes: SVGGElement[] | ArrayLike<SVGGElement>) => {
      const pieceGroup = d3.select(nodes[i]);
      this.drawSinglePiece(pieceGroup, d, scale);
    });

    // Add interaction handlers
    this.addPieceInteractions(pieceGroups);
  }

  /**
   * Draw a single piece with kerf area and actual piece
   */
  private drawSinglePiece(
    group: d3.Selection<SVGGElement, unknown, null, undefined>,
    piece: PlacedPiece,
    scale: number
  ): void {
    // Calculate dimensions
    const pieceActualWidth = convertToUnit(piece.width, piece.unit, this.baseUnit) * scale;
    const pieceActualHeight = convertToUnit(piece.length, piece.unit, this.baseUnit) * scale;
    const packedPieceWidth = piece.packedWidth * scale;
    const packedPieceHeight = piece.packedHeight * scale;

    // Calculate piece position within kerf area (centered)
    const pieceXDisplayOffset = (packedPieceWidth - pieceActualWidth) / 2;
    const pieceYDisplayOffset = (packedPieceHeight - pieceActualHeight) / 2;

    // Draw kerf area (lighter background)
    group.append('rect')
      .attr('class', 'kerf-area')
      .attr('x', 0)
      .attr('y', 0)
      .attr('width', packedPieceWidth)
      .attr('height', packedPieceHeight)
      .style('fill', piece.color + '30') // 30% opacity
      .style('stroke', piece.color + '60') // 60% opacity
      .style('stroke-width', 1);

    // Create gradient for 3D effect
    const gradientId = `gradient-${piece.id}`;
    const defs = this.svg.select('defs');

    const gradient = defs.append('linearGradient')
      .attr('id', gradientId)
      .attr('x1', '0%')
      .attr('y1', '0%')
      .attr('x2', '100%')
      .attr('y2', '100%');

    gradient.append('stop')
      .attr('offset', '0%')
      .attr('stop-color', this.adjustColorBrightness(piece.color, 15));

    gradient.append('stop')
      .attr('offset', '50%')
      .attr('stop-color', piece.color);

    gradient.append('stop')
      .attr('offset', '100%')
      .attr('stop-color', this.adjustColorBrightness(piece.color, -15));

    // Draw shadow
    group.append('rect')
      .attr('class', 'piece-shadow')
      .attr('x', pieceXDisplayOffset + 2)
      .attr('y', pieceYDisplayOffset + 2)
      .attr('width', pieceActualWidth)
      .attr('height', pieceActualHeight)
      .style('fill', 'rgba(0, 0, 0, 0.1)');

    // Draw actual piece
    const pieceRect = group.append('rect')
      .attr('class', 'piece-rect')
      .attr('x', pieceXDisplayOffset)
      .attr('y', pieceYDisplayOffset)
      .attr('width', pieceActualWidth)
      .attr('height', pieceActualHeight)
      .style('fill', `url(#${gradientId})`)
      .style('stroke', '#333')
      .style('stroke-width', 1)
      .style('filter', 'url(#drop-shadow)');

    // Store piece data for interactions
    pieceRect.datum(piece);

    // Draw piece label
    this.drawPieceLabel(group, piece, pieceXDisplayOffset, pieceYDisplayOffset, pieceActualWidth, pieceActualHeight);
  }

  /**
   * Draw piece label with name and dimensions
   */
  private drawPieceLabel(
    group: d3.Selection<SVGGElement, unknown, null, undefined>,
    piece: PlacedPiece,
    x: number,
    y: number,
    width: number,
    height: number
  ): void {
    const textX = x + width / 2;
    const textY = y + height / 2;

    const pieceLabel = piece.name;
    const pieceDims = `${piece.length}${piece.unit} × ${piece.width}${piece.unit}`;

    // Adjust font size and content based on piece size
    if (height > 25 && width > 50) {
      // Draw piece name
      group.append('text')
        .attr('class', 'piece-label-name')
        .attr('x', textX)
        .attr('y', textY - 5)
        .attr('text-anchor', 'middle')
        .attr('dominant-baseline', 'middle')
        .style('font-size', '10px')
        .style('font-weight', 'bold')
        .style('fill', '#fff')
        .style('stroke', '#000')
        .style('stroke-width', '0.5px')
        .style('paint-order', 'stroke')
        .text(pieceLabel);

      // Draw dimensions
      group.append('text')
        .attr('class', 'piece-label-dims')
        .attr('x', textX)
        .attr('y', textY + 8)
        .attr('text-anchor', 'middle')
        .attr('dominant-baseline', 'middle')
        .style('font-size', '8px')
        .style('fill', '#fff')
        .style('stroke', '#000')
        .style('stroke-width', '0.5px')
        .style('paint-order', 'stroke')
        .text(pieceDims);
    } else if (height > 10 && width > 30) {
      group.append('text')
        .attr('class', 'piece-label-name')
        .attr('x', textX)
        .attr('y', textY)
        .attr('text-anchor', 'middle')
        .attr('dominant-baseline', 'middle')
        .style('font-size', '9px')
        .style('font-weight', 'bold')
        .style('fill', '#fff')
        .style('stroke', '#000')
        .style('stroke-width', '0.5px')
        .style('paint-order', 'stroke')
        .text(pieceLabel);
    } else if (height > 10 && width > 10) {
      group.append('text')
        .attr('class', 'piece-label-name')
        .attr('x', textX)
        .attr('y', textY)
        .attr('text-anchor', 'middle')
        .attr('dominant-baseline', 'middle')
        .style('font-size', '7px')
        .style('font-weight', 'bold')
        .style('fill', '#fff')
        .style('stroke', '#000')
        .style('stroke-width', '0.5px')
        .style('paint-order', 'stroke')
        .text(pieceLabel);
    }
  }

  /**
   * Helper method to adjust color brightness
   */
  private adjustColorBrightness(color: string, percent: number): string {
    // Simple brightness adjustment for hex colors
    const num = parseInt(color.replace("#", ""), 16);
    const amt = Math.round(2.55 * percent);
    const R = Math.max(0, Math.min(255, (num >> 16) + amt));
    const G = Math.max(0, Math.min(255, (num >> 8 & 0x00FF) + amt));
    const B = Math.max(0, Math.min(255, (num & 0x0000FF) + amt));
    return "#" + ((1 << 24) + (R << 16) + (G << 8) + B).toString(16).slice(1);
  }

  /**
   * Add interaction handlers to piece groups
   */
  private addPieceInteractions(
    pieceGroups: d3.Selection<SVGGElement, PlacedPiece, SVGGElement, unknown>
  ): void {
    pieceGroups
      .style('cursor', 'pointer')
      .on('click', (_event: MouseEvent, d: PlacedPiece) => {
        this.handlePieceClick(d);
      })
      .on('mouseenter', (event: MouseEvent, d: PlacedPiece) => {
        this.handlePieceMouseEnter(d, event.currentTarget as SVGGElement);
      })
      .on('mouseleave', (event: MouseEvent, d: PlacedPiece) => {
        this.handlePieceMouseLeave(d, event.currentTarget as SVGGElement);
      });
  }

  /**
   * Handle piece click for selection
   */
  private handlePieceClick(piece: PlacedPiece): void {
    // Toggle selection
    if (this.selectedPiece && this.selectedPiece.id === piece.id) {
      this.selectedPiece = null;
      this.updatePieceInfoPanel(null);
    } else {
      this.selectedPiece = piece;
      this.updatePieceInfoPanel(piece);
    }

    // Update visual state
    this.updatePieceVisualStates();

    // Notify parent component
    if (this.props.onPieceSelect) {
      this.props.onPieceSelect(this.selectedPiece);
    }
  }

  /**
   * Handle piece mouse enter for hover effects
   */
  private handlePieceMouseEnter(piece: PlacedPiece, element: SVGGElement): void {
    this.hoveredPiece = piece;

    // Add hover effects
    d3.select(element)
      .select('.piece-rect')
      .style('stroke', '#4A90E2')
      .style('stroke-width', 2)
      .transition()
      .duration(150)
      .style('filter', 'url(#drop-shadow) brightness(1.1)');

    // Show piece info on hover if no piece is selected
    if (!this.selectedPiece) {
      this.updatePieceInfoPanel(piece);
    }
  }

  /**
   * Handle piece mouse leave
   */
  private handlePieceMouseLeave(piece: PlacedPiece, element: SVGGElement): void {
    this.hoveredPiece = null;

    // Remove hover effects if not selected
    if (!this.selectedPiece || this.selectedPiece.id !== piece.id) {
      d3.select(element)
        .select('.piece-rect')
        .style('stroke', '#333')
        .style('stroke-width', 1)
        .transition()
        .duration(150)
        .style('filter', 'url(#drop-shadow)');
    }

    // Hide piece info if no piece is selected
    if (!this.selectedPiece) {
      this.updatePieceInfoPanel(null);
    }
  }

  /**
   * Update visual states for all pieces based on selection
   */
  private updatePieceVisualStates(): void {
    const self = this;
    this.svg.selectAll('.piece-rect').each(function(d: any) {
      const piece = d as PlacedPiece;
      const rect = d3.select(this as SVGRectElement);

      if (self.selectedPiece && self.selectedPiece.id === piece.id) {
        // Selected state
        rect
          .style('stroke', '#FFD700')
          .style('stroke-width', 3)
          .style('filter', 'url(#drop-shadow) brightness(1.2)');
      } else {
        // Normal state
        rect
          .style('stroke', '#333')
          .style('stroke-width', 1)
          .style('filter', 'url(#drop-shadow)');
      }
    });
  }

  /**
   * Update piece information panel
   */
  private updatePieceInfoPanel(piece: PlacedPiece | null): void {
    if (!this.pieceInfoPanel) return;

    if (piece) {
      const grainText = piece.grainDirection !== 'none'
        ? `<br><strong>Grain:</strong> ${piece.grainDirection === 'length' ? 'Along Length' : 'Along Width'}`
        : '';

      this.pieceInfoPanel.innerHTML = `
        <div style="margin-bottom: 4px;"><strong>${piece.name}</strong></div>
        <div style="font-size: 11px; color: #ccc;">
          <strong>Dimensions:</strong> ${piece.length}${piece.unit} × ${piece.width}${piece.unit}${grainText}
        </div>
      `;
      this.pieceInfoPanel.style.display = 'block';
      // Trigger animation
      setTimeout(() => {
        if (this.pieceInfoPanel) {
          this.pieceInfoPanel.style.opacity = '1';
          this.pieceInfoPanel.style.transform = 'translateY(0)';
        }
      }, 10);
    } else {
      if (this.pieceInfoPanel) {
        this.pieceInfoPanel.style.opacity = '0';
        this.pieceInfoPanel.style.transform = 'translateY(-10px)';
        setTimeout(() => {
          if (this.pieceInfoPanel) {
            this.pieceInfoPanel.style.display = 'none';
          }
        }, 200);
      }
    }
  }

  /**
   * Update navigation buttons and indicator
   */
  private updateNavigation(): void {
    const totalSheets = this.props.layouts.length;
    const currentSheet = totalSheets > 0 ? this.props.currentSheetIndex + 1 : 0;

    this.sheetIndicator.textContent = `Sheet ${currentSheet} of ${totalSheets}`;
    this.prevButton.disabled = this.props.currentSheetIndex === 0;
    this.nextButton.disabled = this.props.currentSheetIndex >= totalSheets - 1;
  }

  /**
   * Get currently selected piece
   */
  getSelectedPiece(): PlacedPiece | null {
    return this.selectedPiece;
  }

  /**
   * Set selected piece programmatically
   */
  setSelectedPiece(piece: PlacedPiece | null): void {
    this.selectedPiece = piece;
    this.updatePieceInfoPanel(piece);
    this.updatePieceVisualStates();
    if (this.props.onPieceSelect) {
      this.props.onPieceSelect(piece);
    }
  }

  /**
   * Export SVG as image (PNG/JPEG)
   */
  exportAsImage(format: 'png' | 'jpeg' = 'png'): string {
    const svgElement = this.svg.node();
    if (!svgElement) return '';

    // Create a canvas to convert SVG to image
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return '';

    // Set canvas dimensions
    canvas.width = this.width;
    canvas.height = this.height;

    // Convert SVG to data URL
    const svgData = new XMLSerializer().serializeToString(svgElement);
    const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
    const url = URL.createObjectURL(svgBlob);

    // Create image and draw to canvas
    const img = new Image();
    img.onload = () => {
      ctx.drawImage(img, 0, 0);
      URL.revokeObjectURL(url);
    };
    img.src = url;

    return canvas.toDataURL(`image/${format}`);
  }

  /**
   * Export SVG as string for printing
   */
  exportSVGAsString(): string {
    const svgElement = this.svg.node();
    if (!svgElement) return '';

    // Clone the SVG to avoid modifying the original
    const clonedSvg = svgElement.cloneNode(true) as SVGSVGElement;

    // Set explicit dimensions for printing
    clonedSvg.setAttribute('width', '800');
    clonedSvg.setAttribute('height', '600');
    clonedSvg.setAttribute('viewBox', `0 0 ${this.width} ${this.height}`);

    // Serialize to string
    return new XMLSerializer().serializeToString(clonedSvg);
  }

  /**
   * Generate all sheet visualizations for printing
   */
  generateAllSheetsForPrint(): string[] {
    const svgStrings: string[] = [];

    if (this.props.layouts.length === 0) {
      return svgStrings;
    }

    // Store current state
    const originalSheetIndex = this.props.currentSheetIndex;

    // Generate SVG for each sheet
    for (let i = 0; i < this.props.layouts.length; i++) {
      // Temporarily switch to this sheet
      this.props.currentSheetIndex = i;
      this.render();

      // Export this sheet's SVG
      const svgString = this.exportSVGAsString();
      if (svgString) {
        svgStrings.push(svgString);
      }
    }

    // Restore original state
    this.props.currentSheetIndex = originalSheetIndex;
    this.render();

    return svgStrings;
  }

  /**
   * Get SVG dimensions
   */
  getSVGDimensions(): { width: number; height: number } {
    return {
      width: this.width,
      height: this.height,
    };
  }

  /**
   * Set SVG size
   */
  setSVGSize(width: number, height: number): void {
    this.width = width;
    this.height = height;
    this.svg
      .attr('viewBox', `0 0 ${this.width} ${this.height}`);
    this.render();
  }

  /**
   * Destroy the component
   */
  destroy(): void {
    // Remove event listeners
    this.prevButton.removeEventListener('click', () => {});
    this.nextButton.removeEventListener('click', () => {});

    // Remove SVG
    this.svg.remove();

    // Remove piece info panel
    if (this.pieceInfoPanel && this.pieceInfoPanel.parentNode) {
      this.pieceInfoPanel.parentNode.removeChild(this.pieceInfoPanel);
    }
  }
}
