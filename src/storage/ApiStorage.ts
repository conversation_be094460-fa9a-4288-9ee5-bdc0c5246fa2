/**
 * API-based storage implementation for future server integration
 * This will replace localStorage when server functionality is available
 */

import { BaseMaterial, Project, StorageError, ApiError } from "../types";
import { BaseStorage } from "./StorageInterface";

export class ApiStorage extends BaseStorage {
  private apiEndpoint: string;
  private authToken: string | null = null;

  constructor(apiEndpoint: string) {
    super();
    this.apiEndpoint = apiEndpoint.replace(/\/$/, ""); // Remove trailing slash
  }

  setAuthToken(token: string | null): void {
    this.authToken = token;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<T> {
    const url = `${this.apiEndpoint}${endpoint}`;
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      ...(options.headers as Record<string, string>),
    };

    if (this.authToken) {
      headers["Authorization"] = `Bearer ${this.authToken}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ApiError(
          errorData.message ||
            `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          errorData.code,
          errorData,
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new StorageError(
        `Network error: ${error instanceof Error ? error.message : "Unknown error"}`,
        "makeRequest",
        error,
      );
    }
  }

  async getInventory(): Promise<BaseMaterial[]> {
    try {
      const response = await this.makeRequest<{ inventory: BaseMaterial[] }>(
        "/api/inventory",
      );
      const inventory = response.inventory || [];

      // Validate each material
      inventory.forEach((material) => this.validateMaterial(material));
      return inventory;
    } catch (error) {
      throw new StorageError(
        "Failed to load inventory from server",
        "getInventory",
        error,
      );
    }
  }

  async saveInventory(inventory: BaseMaterial[]): Promise<void> {
    try {
      // Validate all materials before saving
      inventory.forEach((material) => this.validateMaterial(material));

      await this.makeRequest("/api/inventory", {
        method: "PUT",
        body: JSON.stringify({ inventory }),
      });
    } catch (error) {
      throw new StorageError(
        "Failed to save inventory to server",
        "saveInventory",
        error,
      );
    }
  }

  async getProjects(): Promise<Project[]> {
    try {
      const response = await this.makeRequest<{ projects: any[] }>(
        "/api/projects",
      );
      const projects = (response.projects || []).map((data) =>
        this.deserializeProject(data),
      );

      // Validate each project
      projects.forEach((project) => this.validateProject(project));
      return projects;
    } catch (error) {
      throw new StorageError(
        "Failed to load projects from server",
        "getProjects",
        error,
      );
    }
  }

  async saveProjects(projects: Project[]): Promise<void> {
    try {
      // Validate all projects before saving
      projects.forEach((project) => this.validateProject(project));

      const serializedProjects = projects.map((project) =>
        this.serializeProject(project),
      );
      await this.makeRequest("/api/projects", {
        method: "PUT",
        body: JSON.stringify({ projects: serializedProjects }),
      });
    } catch (error) {
      throw new StorageError(
        "Failed to save projects to server",
        "saveProjects",
        error,
      );
    }
  }

  async getCurrentProjectId(): Promise<string | null> {
    try {
      const response = await this.makeRequest<{
        currentProjectId: string | null;
      }>("/api/user/current-project");
      return response.currentProjectId;
    } catch (error) {
      throw new StorageError(
        "Failed to get current project ID from server",
        "getCurrentProjectId",
        error,
      );
    }
  }

  async setCurrentProjectId(projectId: string | null): Promise<void> {
    try {
      await this.makeRequest("/api/user/current-project", {
        method: "PUT",
        body: JSON.stringify({ currentProjectId: projectId }),
      });
    } catch (error) {
      throw new StorageError(
        "Failed to set current project ID on server",
        "setCurrentProjectId",
        error,
      );
    }
  }

  async migrateOldData(): Promise<void> {
    // For API storage, migration would typically be handled server-side
    // This method exists for interface compatibility
    console.log("Migration not needed for API storage - handled server-side");
  }

  async clearStorage(): Promise<void> {
    try {
      await this.makeRequest("/api/user/clear-data", {
        method: "DELETE",
      });
    } catch (error) {
      throw new StorageError(
        "Failed to clear data on server",
        "clearStorage",
        error,
      );
    }
  }

  // Additional API-specific methods
  async syncProject(project: Project): Promise<Project> {
    try {
      this.validateProject(project);
      const serializedProject = this.serializeProject(project);

      const response = await this.makeRequest<{ project: any }>(
        "/api/projects/sync",
        {
          method: "POST",
          body: JSON.stringify({ project: serializedProject }),
        },
      );

      return this.deserializeProject(response.project);
    } catch (error) {
      throw new StorageError(
        "Failed to sync project with server",
        "syncProject",
        error,
      );
    }
  }

  async getSharedProjects(): Promise<Project[]> {
    try {
      const response = await this.makeRequest<{ projects: any[] }>(
        "/api/projects/shared",
      );
      const projects = (response.projects || []).map((data) =>
        this.deserializeProject(data),
      );

      // Validate each project
      projects.forEach((project) => this.validateProject(project));
      return projects;
    } catch (error) {
      throw new StorageError(
        "Failed to load shared projects from server",
        "getSharedProjects",
        error,
      );
    }
  }

  // Health check for API connectivity
  async healthCheck(): Promise<boolean> {
    try {
      await this.makeRequest("/api/health");
      return true;
    } catch {
      return false;
    }
  }

  // Get server capabilities
  async getCapabilities(): Promise<{
    optimization: boolean;
    sharing: boolean;
    authentication: boolean;
    maxProjects: number;
    maxMaterials: number;
  }> {
    try {
      const response = await this.makeRequest<any>("/api/capabilities");
      return {
        optimization: response.optimization || false,
        sharing: response.sharing || false,
        authentication: response.authentication || false,
        maxProjects: response.maxProjects || 10,
        maxMaterials: response.maxMaterials || 100,
      };
    } catch {
      return {
        optimization: false,
        sharing: false,
        authentication: false,
        maxProjects: 10,
        maxMaterials: 100,
      };
    }
  }
}
