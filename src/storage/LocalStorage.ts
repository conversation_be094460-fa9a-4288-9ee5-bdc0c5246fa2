/**
 * localStorage implementation of the storage interface
 * Maintains backward compatibility with existing data
 */

import { BaseMaterial, Project, StorageError } from '../types';
import { BaseStorage } from './StorageInterface';

export class LocalStorage extends BaseStorage {
  // localStorage keys
  private static readonly INVENTORY_KEY = "woodOptimizer.inventory";
  private static readonly PROJECTS_KEY = "woodOptimizer.projects";
  private static readonly CURRENT_PROJECT_KEY = "woodOptimizer.currentProject";
  
  // Legacy keys for backward compatibility
  private static readonly LEGACY_PROJECT_PIECES_KEY = "woodOptimizer.projectPieces";
  private static readonly LEGACY_PROJECT_NAME_KEY = "woodOptimizer.projectName";
  private static readonly LEGACY_SAW_KERF_KEY = "woodOptimizer.sawKerf";
  private static readonly LEGACY_KERF_UNIT_KEY = "woodOptimizer.kerfUnit";

  async getInventory(): Promise<BaseMaterial[]> {
    try {
      const stored = localStorage.getItem(LocalStorage.INVENTORY_KEY);
      if (!stored) return [];
      
      const inventory = JSON.parse(stored) as BaseMaterial[];
      // Validate each material
      inventory.forEach(material => this.validateMaterial(material));
      return inventory;
    } catch (error) {
      throw new StorageError(
        'Failed to load inventory from localStorage',
        'getInventory',
        error
      );
    }
  }

  async saveInventory(inventory: BaseMaterial[]): Promise<void> {
    try {
      // Validate all materials before saving
      inventory.forEach(material => this.validateMaterial(material));
      
      localStorage.setItem(
        LocalStorage.INVENTORY_KEY,
        JSON.stringify(inventory)
      );
    } catch (error) {
      throw new StorageError(
        'Failed to save inventory to localStorage',
        'saveInventory',
        error
      );
    }
  }

  async getProjects(): Promise<Project[]> {
    try {
      const stored = localStorage.getItem(LocalStorage.PROJECTS_KEY);
      if (!stored) return [];
      
      const projectsData = JSON.parse(stored);
      const projects = projectsData.map((data: any) => this.deserializeProject(data));
      
      // Validate each project
      projects.forEach((project: Project) => this.validateProject(project));
      return projects;
    } catch (error) {
      throw new StorageError(
        'Failed to load projects from localStorage',
        'getProjects',
        error
      );
    }
  }

  async saveProjects(projects: Project[]): Promise<void> {
    try {
      // Validate all projects before saving
      projects.forEach(project => this.validateProject(project));
      
      const serializedProjects = projects.map(project => this.serializeProject(project));
      localStorage.setItem(
        LocalStorage.PROJECTS_KEY,
        JSON.stringify(serializedProjects)
      );
    } catch (error) {
      throw new StorageError(
        'Failed to save projects to localStorage',
        'saveProjects',
        error
      );
    }
  }

  async getCurrentProjectId(): Promise<string | null> {
    try {
      const stored = localStorage.getItem(LocalStorage.CURRENT_PROJECT_KEY);
      return stored || null;
    } catch (error) {
      throw new StorageError(
        'Failed to get current project ID from localStorage',
        'getCurrentProjectId',
        error
      );
    }
  }

  async setCurrentProjectId(projectId: string | null): Promise<void> {
    try {
      if (projectId) {
        localStorage.setItem(LocalStorage.CURRENT_PROJECT_KEY, projectId);
      } else {
        localStorage.removeItem(LocalStorage.CURRENT_PROJECT_KEY);
      }
    } catch (error) {
      throw new StorageError(
        'Failed to set current project ID in localStorage',
        'setCurrentProjectId',
        error
      );
    }
  }

  async migrateOldData(): Promise<void> {
    try {
      // Check if migration is needed
      const hasNewData = localStorage.getItem(LocalStorage.PROJECTS_KEY);
      if (hasNewData) return; // Already migrated

      // Check for old project data
      const oldPieces = localStorage.getItem(LocalStorage.LEGACY_PROJECT_PIECES_KEY);
      const oldName = localStorage.getItem(LocalStorage.LEGACY_PROJECT_NAME_KEY);
      const oldKerf = localStorage.getItem(LocalStorage.LEGACY_SAW_KERF_KEY);
      const oldKerfUnit = localStorage.getItem(LocalStorage.LEGACY_KERF_UNIT_KEY);

      if (oldPieces || oldName) {
        const migratedProject: Project = {
          id: this.generateId(),
          name: oldName || "My Project",
          pieces: oldPieces ? JSON.parse(oldPieces) : [],
          sawKerf: oldKerf ? parseFloat(oldKerf) : 3,
          kerfUnit: (oldKerfUnit as any) || "mm",
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        await this.saveProjects([migratedProject]);
        await this.setCurrentProjectId(migratedProject.id);

        // Clean up old keys
        localStorage.removeItem(LocalStorage.LEGACY_PROJECT_PIECES_KEY);
        localStorage.removeItem(LocalStorage.LEGACY_PROJECT_NAME_KEY);
        localStorage.removeItem(LocalStorage.LEGACY_SAW_KERF_KEY);
        localStorage.removeItem(LocalStorage.LEGACY_KERF_UNIT_KEY);

        console.log('Successfully migrated legacy project data');
      }
    } catch (error) {
      throw new StorageError(
        'Failed to migrate old data',
        'migrateOldData',
        error
      );
    }
  }

  async clearStorage(): Promise<void> {
    try {
      localStorage.removeItem(LocalStorage.INVENTORY_KEY);
      localStorage.removeItem(LocalStorage.PROJECTS_KEY);
      localStorage.removeItem(LocalStorage.CURRENT_PROJECT_KEY);
      
      // Also clear legacy keys if they exist
      localStorage.removeItem(LocalStorage.LEGACY_PROJECT_PIECES_KEY);
      localStorage.removeItem(LocalStorage.LEGACY_PROJECT_NAME_KEY);
      localStorage.removeItem(LocalStorage.LEGACY_SAW_KERF_KEY);
      localStorage.removeItem(LocalStorage.LEGACY_KERF_UNIT_KEY);
    } catch (error) {
      throw new StorageError(
        'Failed to clear localStorage',
        'clearStorage',
        error
      );
    }
  }

  // Utility method for generating IDs
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  // Check if localStorage is available
  static isAvailable(): boolean {
    try {
      const test = '__localStorage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  // Get storage usage information
  getStorageInfo(): { used: number; available: number; percentage: number } {
    try {
      let used = 0;
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          used += localStorage[key].length + key.length;
        }
      }

      // Estimate available space (most browsers have ~5-10MB limit)
      const estimated = 5 * 1024 * 1024; // 5MB estimate
      const available = estimated - used;
      const percentage = (used / estimated) * 100;

      return { used, available, percentage };
    } catch {
      return { used: 0, available: 0, percentage: 0 };
    }
  }
}
